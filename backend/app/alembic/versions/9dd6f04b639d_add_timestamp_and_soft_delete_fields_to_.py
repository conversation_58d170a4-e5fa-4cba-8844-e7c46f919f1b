"""Add timestamp and soft delete fields to item table

Revision ID: 9dd6f04b639d
Revises: a3d16e73aae8
Create Date: 2025-08-01 16:10:24.536463

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9dd6f04b639d'
down_revision = 'a3d16e73aae8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # 添加时间戳字段，先设为可空
    op.add_column('item', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('item', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('item', sa.Column('deleted_at', sa.DateTime(), nullable=True))

    # 为现有记录设置默认时间戳
    op.execute("UPDATE item SET created_at = NOW(), updated_at = NOW() WHERE created_at IS NULL")

    # 将字段设为非空（除了 deleted_at）
    op.alter_column('item', 'created_at', nullable=False)
    op.alter_column('item', 'updated_at', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('item', 'deleted_at')
    op.drop_column('item', 'updated_at')
    op.drop_column('item', 'created_at')
    # ### end Alembic commands ###
