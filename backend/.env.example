# FastAPI 项目环境配置示例

# Domain
DOMAIN=localhost
FRONTEND_HOST=http://localhost:5173

# Environment: local, staging, production
ENVIRONMENT=local

# Logging Configuration - 专业日志配置
# 日志级别: DEBUG(开发调试) / INFO(正常流程) / WARNING(小异常) / ERROR(逻辑错误)
LOG_LEVEL=DEBUG
# 启用详细的请求/响应日志记录 (true/false)
LOG_REQUEST_DETAILS=true
# 启用函数参数日志记录 (true/false)
LOG_FUNCTION_PARAMS=true
# 日志输出格式: console(控制台可读) / json(JSON格式) / both(两者)
LOG_FORMAT=console
# 启用日志文件输出 (true/false)
LOG_FILE_ENABLED=true
# 日志文件最大大小 (MB)
LOG_FILE_MAX_SIZE=10
# 日志文件备份数量
LOG_FILE_BACKUP_COUNT=7

PROJECT_NAME="Full Stack FastAPI Project"
STACK_NAME=full-stack-fastapi-project

# Backend
BACKEND_CORS_ORIGINS="http://localhost,http://localhost:5173,https://localhost,https://localhost:5173"
SECRET_KEY=changethis
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=changethis

# Emails
SMTP_HOST=
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>
SMTP_TLS=True
SMTP_SSL=False
SMTP_PORT=587

# Postgres
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_DB=fastapi_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=changethis

SENTRY_DSN=

# Docker
DOCKER_IMAGE_BACKEND=backend
DOCKER_IMAGE_FRONTEND=frontend
